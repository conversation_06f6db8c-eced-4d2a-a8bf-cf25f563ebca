//视频编辑页

import UIKit
import TXLiteAVSDK_UGC
import SnapKit
import Kingfisher
import AVFoundation

class VideoEditViewController: BaseViewController, TXVideoPreviewListener, MusicPanelViewDelegate, FilterPanelViewDelegate, BeautyPanelViewDelegate, TXVideoGenerateListener, TXVideoPublishListener {
    
    func filterPanelDidTouchOriginal(_ state: UIGestureRecognizer.State) {
        print("[编辑页] 原图滤镜: \(state)")
    }
    
    private let videoPath: String
    private let asset: AVAsset?
    private let bgm: MusicItem?
    private var videoPreview: TXVideoEditer?
    private var isPlaying = false
    private var videoDuration: CGFloat = 0
    private var generatedVideoPath: String?

    // MARK: - 存草稿相关属性
    /// 是否正在保存草稿
    private var isSavingDraft = false
    /// 是否在保存草稿后退出
    private var shouldExitAfterSavingDraft = false
    /// 腾讯云 VOD 上传签名
    private var uploadSignature: String?
    /// 发布器
    private var ugcPublisher: TXUGCPublish?
    /// 发布进度弹窗
    private var publishProgressAlert: UIAlertController?
    /// 进度条
    private var publishProgressView: UIProgressView?

    // MARK: - 音频控制相关属性
    /// 原声音量 (0.0 ~ 1.0)
    private var originalSoundVolume: Float = 1.0
    /// 配乐音量 (0.0 ~ 1.0)
    private var musicVolume: Float = 1.0
    /// 原声是否开启
    private var isOriginalSoundOn: Bool = true
    /// 配乐是否开启
    private var isMusicSoundOn: Bool = true
    
    // MARK: - 弹窗面板
    private lazy var musicPanelView: MusicPanelView = {
        let view = MusicPanelView()
        view.delegate = self
        view.isHidden = true
        return view
    }()
    private lazy var filterPanelView: FilterPanelView = {
        let view = FilterPanelView()
        view.delegate = self
        view.isHidden = true
        return view
    }()
    private lazy var beautyPanelView: BeautyPanelView = {
        let view = BeautyPanelView()
        view.delegate = self
        view.isHidden = true
        return view
    }()
    private lazy var dimmingOverlayView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.clear // 改为完全透明
        view.isHidden = true
        let tap = UITapGestureRecognizer(target: self, action: #selector(hideAllPanels))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    // MARK: - UI Components
    private lazy var previewView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    private lazy var rightButtonStack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.alignment = .center
        stack.distribution = .equalSpacing
        stack.spacing = 16
        let items = [
            ("滤镜", "video_editing_filter_selection"),
            ("美颜", "video_editing_beauty_selection")
        ]
        for (idx, item) in items.enumerated() {
            let icon = UIImage(named: item.1)
            let btn = VerticalIconButton(image: icon, title: item.0)
            btn.tag = idx + 1 // tag顺延
            btn.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(rightButtonTappedFromCustom(_:))))
            stack.addArrangedSubview(btn)
        }
        return stack;
    }()
    
    private lazy var progressView: UIProgressView = {
        let progress = UIProgressView(progressViewStyle: .default)
        progress.progress = 0.0
        progress.trackTintColor = UIColor.white.withAlphaComponent(0.3)
        progress.progressTintColor = UIColor.white
        return progress
    }()
    
    private lazy var playButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "play_type_cell"), for: .normal)
        button.addTarget(self, action: #selector(playButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var draftButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("存草稿", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(hex: "#EFEFEF").withAlphaComponent(0.5)
        button.layer.cornerRadius = 8
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .medium)
        button.addTarget(self, action: #selector(draftButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var nextButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("下一步", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(hex: "#FF8F1F")
        button.layer.cornerRadius = 8
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .medium)
        button.addTarget(self, action: #selector(nextButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 恢复左上角返回按钮
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .system)
        let icon = UIImage(systemName: "xmark")
        button.setImage(icon, for: .normal)
        button.tintColor = .white
        button.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        button.contentHorizontalAlignment = .center
        button.contentVerticalAlignment = .center
        button.imageEdgeInsets = UIEdgeInsets(top: 3, left: 3, bottom: 3, right: 3) // 30-24=6, 平分上下左右
        return button
    }()
    
    private lazy var musicSelectBar: MusicSelectBar = {
        let bar = MusicSelectBar()
        bar.translatesAutoresizingMaskIntoConstraints = false
        bar.onTap = { [weak self] in
            self?.showMusicPanel()
        }
        bar.onClear = { [weak self] in
            self?.setSelectedMusic(nil)
        }
        return bar
    }()
    
    // MARK: - Initialization
    init(videoPath: String, asset: AVAsset?, bgm: MusicItem?) {
        self.videoPath = videoPath
        self.asset = asset
        self.bgm = bgm
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        print("[调试] VideoEditViewController viewDidLoad, videoPath=\(videoPath)")
        setupUI()
        setupVideoEditer()
        // 打印BGM信息
        if let bgm = bgm {
            print("[编辑页] 传入BGM信息: \(bgm.title), 路径: \(bgm.localFileURL?.path ?? "无")")
        } else {
            print("[编辑页] 未传入BGM信息")
        }
        // 添加弹窗和蒙版到主视图
        view.addSubview(dimmingOverlayView)
        view.addSubview(musicPanelView)
        view.addSubview(filterPanelView)
        view.addSubview(beautyPanelView)
        view.addSubview(musicSelectBar)
        // 设置初始约束，分别设置不同高度
        musicPanelView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(450+WindowUtil.safeAreaBottom)
        }
        filterPanelView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(270+WindowUtil.safeAreaBottom)
        }
        beautyPanelView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(170+WindowUtil.safeAreaBottom)
        }
        dimmingOverlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        musicSelectBar.snp.makeConstraints { make in
            make.top.equalTo(navBar.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
            make.height.equalTo(34)
            make.width.greaterThanOrEqualTo(88)
            make.width.lessThanOrEqualTo(150)
        }
        
        // 自动开始预览
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            if let videoPreview = self.videoPreview, self.videoDuration > 0 {
                videoPreview.startPlay(fromTime: 0, toTime: self.videoDuration)
                self.isPlaying = true
                self.updatePlayButtonIcon()
            }
        }
        
        // 在viewDidLoad最后，录制页传入BGM时自动选中
        if let bgm = bgm {
            setSelectedMusic(bgm)
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        videoPreview?.stopPlay()
        isPlaying = false
        updatePlayButtonIcon()
    }
    
    deinit {
        // 显式释放TXVideoEditer，防止预览叠加和资源泄漏
        videoPreview?.stopPlay()
        videoPreview = nil
        print("[编辑页] VideoEditViewController已释放，TXVideoEditer已清理")
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .black
        showBackButton = false
        navBar.backgroundColor = .black
        contentView.backgroundColor = .black

        // 隐藏导航栏底部分割线
        for subview in navBar.subviews {
            if subview.backgroundColor == UIColor(hex: "#EEEEEE") {
                subview.isHidden = true
            }
        }
        
        // 添加新UI
        contentView.addSubview(previewView)
        contentView.addSubview(rightButtonStack)
        contentView.addSubview(progressView)
        contentView.addSubview(playButton)
        contentView.addSubview(draftButton)
        contentView.addSubview(nextButton)
        navBar.addSubview(closeButton)
        contentView.addSubview(musicSelectBar)
        // 新增底部按钮等宽约束辅助视图
        let bottomButtonSpacer = UIView()
        bottomButtonSpacer.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(bottomButtonSpacer)
        
        previewView.translatesAutoresizingMaskIntoConstraints = false
        rightButtonStack.translatesAutoresizingMaskIntoConstraints = false
        progressView.translatesAutoresizingMaskIntoConstraints = false
        playButton.translatesAutoresizingMaskIntoConstraints = false
        draftButton.translatesAutoresizingMaskIntoConstraints = false
        nextButton.translatesAutoresizingMaskIntoConstraints = false
        bottomButtonSpacer.translatesAutoresizingMaskIntoConstraints = false
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        musicSelectBar.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 1. 预览View顶部对齐contentView，宽度等于contentView，高度为16:9
            previewView.topAnchor.constraint(equalTo: contentView.topAnchor),
            previewView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            previewView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            previewView.heightAnchor.constraint(equalTo: previewView.widthAnchor, multiplier: 16.0/9.0),
            
            // 2. 右侧中段按钮
            rightButtonStack.centerYAnchor.constraint(equalTo: contentView.centerYAnchor, constant: -150),
            rightButtonStack.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -10),
            
            // 3. 进度条（底部按钮上方10pt）
            progressView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 11),
            progressView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -11),
            progressView.bottomAnchor.constraint(equalTo: draftButton.topAnchor, constant: -10),
            progressView.heightAnchor.constraint(equalToConstant: 4),
            
            // 4. 播放按钮（右下角，进度条上方9pt，右边距14pt，30*30pt）
            playButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -14),
            playButton.bottomAnchor.constraint(equalTo: progressView.topAnchor, constant: -9),
            playButton.widthAnchor.constraint(equalToConstant: 30),
            playButton.heightAnchor.constraint(equalToConstant: 30),
            
            // 5. 存草稿按钮（左6pt，底部24pt，高43pt，右侧与下一步按钮间隔8pt）
            draftButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 6),
            draftButton.bottomAnchor.constraint(equalTo: contentView.safeAreaLayoutGuide.bottomAnchor, constant: -24),
            draftButton.heightAnchor.constraint(equalToConstant: 43),
            // 6. 下一步按钮（右6pt，底部24pt，高43pt，左侧与存草稿按钮间隔8pt）
            nextButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -6),
            nextButton.bottomAnchor.constraint(equalTo: contentView.safeAreaLayoutGuide.bottomAnchor, constant: -24),
            nextButton.heightAnchor.constraint(equalToConstant: 43),
            // 两按钮间距8pt
            nextButton.leadingAnchor.constraint(equalTo: draftButton.trailingAnchor, constant: 8),
            // 两按钮等宽
            draftButton.widthAnchor.constraint(equalTo: nextButton.widthAnchor),
            
            // 左上角返回按钮（navBar内，左15pt，垂直居中，30x30pt）
            closeButton.leadingAnchor.constraint(equalTo: navBar.leadingAnchor, constant: 15),
            closeButton.centerYAnchor.constraint(equalTo: navBar.centerYAnchor),
            closeButton.widthAnchor.constraint(equalToConstant: 30),
            closeButton.heightAnchor.constraint(equalToConstant: 30),
            
            // 新增音乐选择Bar，导航栏下方10pt，居中，宽度动态88~150pt，高34pt
            musicSelectBar.topAnchor.constraint(equalTo: navBar.bottomAnchor, constant: 10),
            musicSelectBar.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            musicSelectBar.heightAnchor.constraint(equalToConstant: 34),
            musicSelectBar.widthAnchor.constraint(greaterThanOrEqualToConstant: 88),
            musicSelectBar.widthAnchor.constraint(lessThanOrEqualToConstant: 150),
        ])
    }
    
    private func setupVideoEditer() {
        print("[调试] setupVideoEditer 入参 videoPath: \(videoPath)")
        let param = TXPreviewParam()
        param.videoView = previewView
        param.renderMode = .PREVIEW_RENDER_MODE_FILL_EDGE
        videoPreview = TXVideoEditer(preview: param)
        if let asset = asset {
            print("[调试] 使用 setVideoAsset 初始化")
            let result = videoPreview?.setVideoAsset(asset)
            print("[调试] setVideoAsset 返回值: \(String(describing: result))")
        } else {
            print("[调试] 使用 setVideoPath 初始化")
            let result = videoPreview?.setVideoPath(videoPath)
            print("[调试] setVideoPath 返回值: \(String(describing: result))")
        }
        videoPreview?.previewDelegate = self
        print("[调试] 传入videoPath:\(videoPath)")
        videoPreview?.preview(atTime: 0)
        // 获取视频时长
        if let info = TXVideoInfoReader.getVideoInfo(videoPath) {
            videoDuration = CGFloat(info.duration)
            print("[调试] 获取视频信息成功，时长: \(videoDuration)，封面: \(String(describing: info.coverImage))")
        } else {
            print("[调试] 获取视频信息失败: \(videoPath)")
        }
        // 新增：初始化设置BGM
        if let bgm = self.bgm, let bgmPath = bgm.localFileURL?.path {
            videoPreview?.setBGM(bgmPath) { [weak self] result in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    print("[调试] setBGM 返回: \(result)")
                    if result == 0 {
                        // BGM设置成功后，应用初始音量设置
                        let actualMusicVolume = self.isMusicSoundOn ? self.musicVolume : 0.0
                        let actualOriginalVolume = self.isOriginalSoundOn ? self.originalSoundVolume : 0.0

                        self.videoPreview?.setBGMVolume(actualMusicVolume)
                        self.videoPreview?.setVideoVolume(actualOriginalVolume)

                        print("[调试] 初始化音量设置 - 配乐: \(actualMusicVolume), 原声: \(actualOriginalVolume)")

                        self.videoPreview?.startPlay(fromTime: 0, toTime: self.videoDuration)
                        self.isPlaying = true
                    } else {
                        self.isPlaying = false
                    }
                    self.updatePlayButtonIcon()
                }
            }
        } else {
            // 没有BGM时，也要设置原声音量
            let actualOriginalVolume = self.isOriginalSoundOn ? self.originalSoundVolume : 0.0
            videoPreview?.setVideoVolume(actualOriginalVolume)
            print("[调试] 无BGM，设置原声音量: \(actualOriginalVolume)")
        }
    }
    
    // MARK: - Actions
    @objc private func rightButtonTappedFromCustom(_ sender: UITapGestureRecognizer) {
        guard let btn = sender.view as? VerticalIconButton else { return }
        switch btn.tag {
        case 1:
            showFilterPanel()
        case 2:
            showBeautyPanel()
        default:
            break
        }
    }
    
    @objc private func playButtonTapped() {
        guard let videoPreview = videoPreview else { return }
        if isPlaying {
            videoPreview.pausePlay()
            isPlaying = false
        } else {
            videoPreview.startPlay(fromTime: 0, toTime: videoDuration)
            isPlaying = true
        }
        updatePlayButtonIcon()
    }
    
    @objc private func draftButtonTapped() {
        // 防止重复点击
        guard !isSavingDraft else { return }

        // 先生成编辑后的视频
        let outputPath = NSTemporaryDirectory().appending("draft_video_\(Int(Date().timeIntervalSince1970)).mp4")
        self.generatedVideoPath = outputPath

        // 禁用按钮并显示状态
        draftButton.isEnabled = false
        draftButton.setTitle("生成中...", for: .normal)

        // 设置标记
        isSavingDraft = true

        // 设置生成回调
        videoPreview?.generateDelegate = self
        videoPreview?.generateVideo(.VIDEO_COMPRESSED_720P, videoOutputPath: outputPath)
    }
    
    @objc private func nextButtonTapped() {
        let outputPath = NSTemporaryDirectory().appending("edited_video_\(Int(Date().timeIntervalSince1970)).mp4")
        self.generatedVideoPath = outputPath
        nextButton.isEnabled = false
        videoPreview?.generateDelegate = self
        videoPreview?.generateVideo(.VIDEO_COMPRESSED_720P, videoOutputPath: outputPath)
    }
    
    @objc private func closeButtonTapped() {
        // 创建返回拦截弹窗
        let popup = BackInterceptPopupView(frame: .zero)
        popup.onActionSelected = { [weak self] action in
            guard let self = self else { return }
            switch action {
            case .discard:
                // 不保存直接返回
                if let navigationController = self.navigationController {
                    navigationController.popViewController(animated: true)
                } else {
                    self.dismiss(animated: true)
                }
            case .saveDraft:
                // 保存草稿并返回
                self.saveDraftAndExit()
            }
        }
        // 显示弹窗，由于使用的是自定义closeButton，需要传递closeButton作为参考位置
        popup.present(below: self.closeButton)
    }

    /// 保存草稿并退出
    private func saveDraftAndExit() {
        // 防止重复点击
        guard !isSavingDraft else { return }

        // 先生成编辑后的视频
        let outputPath = NSTemporaryDirectory().appending("draft_video_\(Int(Date().timeIntervalSince1970)).mp4")
        self.generatedVideoPath = outputPath

        // 禁用按钮并显示状态
        draftButton.isEnabled = false
        draftButton.setTitle("生成中...", for: .normal)

        // 设置标记，并标记为退出模式
        isSavingDraft = true
        shouldExitAfterSavingDraft = true

        // 设置生成回调
        videoPreview?.generateDelegate = self
        videoPreview?.generateVideo(.VIDEO_COMPRESSED_720P, videoOutputPath: outputPath)
    }
    
    // MARK: - Edit Functions
    private func applyFilter(filter: FilterItem) {
        // 1. 获取本地滤镜图片路径
        guard let fileName = URL(string: filter.contentFile)?.lastPathComponent else {
            print("[编辑页] 无法获取滤镜文件名"); return
        }
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let filterURL = documentsDirectory.appendingPathComponent("Filters/\(fileName)")
        
        // 2. 检查本地是否存在
        if FileManager.default.fileExists(atPath: filterURL.path) {
            if let filterImage = UIImage(contentsOfFile: filterURL.path) {
                videoPreview?.setFilter(filterImage)
                videoPreview?.setSpecialRatio(filter.intensity)
                print("[编辑页] 本地滤镜应用成功: \(filterURL.path)")
            } else {
                print("[编辑页] 无法加载本地滤镜图片")
            }
        } else {
            // 3. 本地无则下载
            print("[编辑页] 本地无滤镜，开始下载: \(filter.contentFile)")
            downloadFilterImage(urlString: filter.contentFile, destination: filterURL) { [weak self] success in
                guard let self = self else { return }
                if success, let filterImage = UIImage(contentsOfFile: filterURL.path) {
                    self.videoPreview?.setFilter(filterImage)
                    self.videoPreview?.setSpecialRatio(filter.intensity)
                    print("[编辑页] 下载并应用滤镜成功: \(filterURL.path)")
                } else {
                    print("[编辑页] 滤镜下载失败")
                }
            }
        }
    }
    
    // 下载滤镜图片到本地
    private func downloadFilterImage(urlString: String, destination: URL, completion: @escaping (Bool) -> Void) {
        guard let url = URL(string: urlString) else { completion(false); return }
        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            if let data = data, error == nil {
                do {
                    // 确保Filters目录存在
                    let dir = destination.deletingLastPathComponent()
                    if !FileManager.default.fileExists(atPath: dir.path) {
                        try FileManager.default.createDirectory(at: dir, withIntermediateDirectories: true, attributes: nil)
                    }
                    try data.write(to: destination)
                    DispatchQueue.main.async { completion(true) }
                } catch {
                    print("[编辑页] 保存滤镜图片失败: \(error)")
                    DispatchQueue.main.async { completion(false) }
                }
            } else {
                print("[编辑页] 下载滤镜图片失败: \(error?.localizedDescription ?? "未知错误")")
                DispatchQueue.main.async { completion(false) }
            }
        }
        task.resume()
    }
    
    private func addMusic() {
        // TODO: 添加音乐
    }
    
    private func applyBeauty() {
        // TODO: 美颜功能
    }
    
    private func updatePlayButtonIcon() {
        let imageName = isPlaying ? "pause_type_cell" : "play_type_cell"
        playButton.setImage(UIImage(named: imageName), for: .normal)
    }
    
    // MARK: - TXVideoPreviewListener
    func onPreviewProgress(_ time: CGFloat) {
        guard videoDuration > 0 else { return }
        progressView.progress = Float(time / videoDuration)
    }
    
    func onPreviewFinished() {
        isPlaying = false
        updatePlayButtonIcon()
        progressView.progress = 1.0 // 或者重置为0.0
    }
    
    // MARK: - 弹窗显示/隐藏逻辑
    private func showMusicPanel() {
        hideAllPanels()
        dimmingOverlayView.isHidden = false
        musicPanelView.isHidden = false
        // 首次弹出自动加载音乐列表
        if musicPanelView.musicItems.isEmpty {
            musicPanelView.loadMusicData(isFavorites: false, refreshing: true)
        }
        if let bgm = self.bgm {
            musicPanelView.insertAndSelectBGM(bgm)
        }
        view.bringSubviewToFront(dimmingOverlayView)
        view.bringSubviewToFront(musicPanelView)
    }
    private func showFilterPanel() {
        hideAllPanels()
        dimmingOverlayView.isHidden = false
        filterPanelView.isHidden = false
        view.bringSubviewToFront(dimmingOverlayView)
        view.bringSubviewToFront(filterPanelView)
    }
    private func showBeautyPanel() {
        hideAllPanels()
        dimmingOverlayView.isHidden = false
        beautyPanelView.isHidden = false
        view.bringSubviewToFront(dimmingOverlayView)
        view.bringSubviewToFront(beautyPanelView)
    }
    @objc private func hideAllPanels() {
        musicPanelView.isHidden = true
        filterPanelView.isHidden = true
        beautyPanelView.isHidden = true
        dimmingOverlayView.isHidden = true
    }
    
    // MARK: - MusicPanelViewDelegate
    func musicPanelDidSelectMusic(_ musicItem: MusicItem?) {
        if let music = musicItem {
            print("[编辑页] 选择音乐: \(music.title)")
            setSelectedMusic(music)
        } else {
            print("[编辑页] 取消选中音乐，清除BGM")
            setSelectedMusic(nil)
        }
    }
    func musicPanelDidToggleFavorite(_ musicItem: MusicItem, at indexPath: IndexPath) {
        print("[编辑页] 切换收藏: \(musicItem.title)")
    }
    func musicPanelDidToggleOriginalSound(_ isOn: Bool) {
        print("[编辑页] 原声开关: \(isOn)")
        isOriginalSoundOn = isOn

        // 根据开关状态设置原声音量
        let actualVolume = isOn ? originalSoundVolume : 0.0
        videoPreview?.setVideoVolume(actualVolume)
        print("[编辑页] 设置原声音量: \(actualVolume)")
    }

    func musicPanelDidToggleMusicSound(_ isOn: Bool) {
        print("[编辑页] 配乐开关: \(isOn)")
        isMusicSoundOn = isOn

        // 根据开关状态设置配乐音量
        let actualVolume = isOn ? musicVolume : 0.0
        videoPreview?.setBGMVolume(actualVolume)
        print("[编辑页] 设置配乐音量: \(actualVolume)")
    }

    func musicPanelDidAdjustOriginalVolume(_ volume: Float) {
        print("[编辑页] 原声音量调整: \(volume)")
        originalSoundVolume = volume

        // 只有在原声开启时才应用音量变化
        let actualVolume = isOriginalSoundOn ? volume : 0.0
        videoPreview?.setVideoVolume(actualVolume)
        print("[编辑页] 应用原声音量: \(actualVolume)")
    }

    func musicPanelDidAdjustMusicVolume(_ volume: Float) {
        print("[编辑页] 配乐音量调整: \(volume)")
        musicVolume = volume

        // 只有在配乐开启时才应用音量变化
        let actualVolume = isMusicSoundOn ? volume : 0.0
        videoPreview?.setBGMVolume(actualVolume)
        print("[编辑页] 应用配乐音量: \(actualVolume)")
    }
    // MARK: - FilterPanelViewDelegate
    func filterPanelDidSelectFilter(_ filter: FilterItem) {
        print("[编辑页] 选择滤镜: \(filter.name)")
        // 应用滤镜
        applyFilter(filter: filter)
    }
    func filterPanelDidTapDownload(for filter: FilterItem) {
        print("[编辑页] 下载滤镜: \(filter.name)")
        // 主动下载滤镜
        guard let fileName = URL(string: filter.contentFile)?.lastPathComponent else { return }
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let filterURL = documentsDirectory.appendingPathComponent("Filters/\(fileName)")
        downloadFilterImage(urlString: filter.contentFile, destination: filterURL) { success in
            if success {
                print("[编辑页] 手动下载滤镜成功")
            } else {
                print("[编辑页] 手动下载滤镜失败")
            }
        }
    }
    func filterPanelDidChangeIntensity(_ intensity: Float, for filter: FilterItem) {
        print("[编辑页] 滤镜强度: \(intensity), 滤镜: \(filter.name)")
        // 实时调整滤镜强度
        videoPreview?.setSpecialRatio(intensity)
    }
    // MARK: - BeautyPanelViewDelegate
    func beautyPanelDidSelectType(_ beauty: BeautyItem) {
        print("选择美颜: \(beauty.name), ID: \(beauty.id)")
        
        // 根据featureCode设置不同的美颜效果
        guard let featureCode = beauty.featureCode else {
            // 对于"无"特效，关闭所有美颜效果
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyStyle(.nature)
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyLevel(0)
            TXUGCRecord.shareInstance().getBeautyManager().setWhitenessLevel(0)
            TXUGCRecord.shareInstance().getBeautyManager().setRuddyLevel(0)
            print("关闭美颜效果")
            //            videoPreview?.setBeautyFilter(<#T##beautyLevel: Float##Float#>, setWhiteningLevel: <#T##Float#>)
            return
        }
        
        // 应用美颜效果并设置强度
        applyBeautyEffect(for: beauty)
    }
    
    func beautyPanelDidChangeIntensity(_ intensity: Float, for beauty: BeautyItem) {
        print("美颜强度变化: \(intensity), 美颜: \(beauty.name)")
        
        // 应用美颜效果
        applyBeautyEffect(for: beauty)
    }
    func beautyPanelDidTapDownload(for beauty: BeautyItem) {
        print("[编辑页] 下载美颜: \(beauty.name)")
    }
    func beautyPanelDidTapOriginal() {
        print("[编辑页] 显示原图")
    }
    
    private func applyBeautyEffect(for beauty: BeautyItem) {
        // 0~1 映射到 0~9
        let sdkValue = beauty.intensity * 9
        
        guard let featureCode = beauty.featureCode else {
            // "无"效果
            videoPreview?.setBeautyFilter(0, setWhiteningLevel: 0)
            print("关闭美颜效果")
            return
        }
        
        switch featureCode {
        case "1": // 磨皮
            videoPreview?.setBeautyFilter(sdkValue, setWhiteningLevel: 0)
            print("应用磨皮效果，强度: \(sdkValue)")
        case "2": // 美白
            videoPreview?.setBeautyFilter(0, setWhiteningLevel: sdkValue)
            print("应用美白效果，强度: \(sdkValue)")
        default:
            print("未知的美颜效果代码: \(featureCode)")
            videoPreview?.setBeautyFilter(0, setWhiteningLevel: 0)
        }
    }
    
    private func setSelectedMusic(_ music: MusicItem?) {
        if let music = music {
            musicSelectBar.setSelectedMusic(music)
            // 设置BGM
            if let bgmPath = music.localFileURL?.path {
                videoPreview?.setBGM(bgmPath) { [weak self] result in
                    guard let self = self else { return }
                    DispatchQueue.main.async {
                        if result == 0 {
                            // BGM设置成功后，应用当前的音量设置
                            let actualMusicVolume = self.isMusicSoundOn ? self.musicVolume : 0.0
                            let actualOriginalVolume = self.isOriginalSoundOn ? self.originalSoundVolume : 0.0

                            self.videoPreview?.setBGMVolume(actualMusicVolume)
                            self.videoPreview?.setVideoVolume(actualOriginalVolume)

                            print("[编辑页] BGM设置成功，应用音量 - 配乐: \(actualMusicVolume), 原声: \(actualOriginalVolume)")

                            self.videoPreview?.startPlay(fromTime: 0, toTime: self.videoDuration)
                            self.isPlaying = true
                        } else {
                            print("[编辑页] BGM设置失败，错误码: \(result)")
                            self.isPlaying = false
                        }
                        self.updatePlayButtonIcon()
                    }
                }
            }
        } else {
            musicSelectBar.setUnselected()
            videoPreview?.setBGM("") { [weak self] _ in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    print("[编辑页] 清除BGM")
                    self.isPlaying = false
                    self.updatePlayButtonIcon()
                }
            }
        }
    }
    
    // MARK: - TXVideoGenerateListener
    func onGenerateComplete(_ result: TXGenerateResult) {
        DispatchQueue.main.async {
            if self.isSavingDraft {
                // 存草稿流程
                self.draftButton.isEnabled = true
                self.draftButton.setTitle("存草稿", for: .normal)

                print("[存草稿] 视频生成完成: retCode=\(result.retCode), videoPath=\(self.generatedVideoPath ?? "nil")")

                if result.retCode == .GENERATE_RESULT_OK, let videoPath = self.generatedVideoPath {
                    // 检查文件是否存在
                    let fileExists = FileManager.default.fileExists(atPath: videoPath)
                    print("[存草稿] 视频文件是否存在: \(fileExists), 路径: \(videoPath)")

                    if fileExists {
                        // 视频生成成功，开始上传流程
                        self.getTXSign()
                    } else {
                        // 文件不存在
                        self.isSavingDraft = false
                        let alert = UIAlertController(title: "生成失败", message: "视频文件不存在", preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default))
                        self.present(alert, animated: true)
                    }
                } else {
                    // 视频生成失败
                    self.isSavingDraft = false
                    let alert = UIAlertController(title: "生成失败", message: result.descMsg, preferredStyle: .alert)
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    self.present(alert, animated: true)
                }
            } else {
                // 下一步流程
                self.nextButton.isEnabled = true
                if result.retCode == .GENERATE_RESULT_OK, let videoPath = self.generatedVideoPath {
                    // 一次性获取视频信息
                    if let info = TXVideoInfoReader.getVideoInfo(videoPath) {
                        let coverImage = info.coverImage
                        let duration = info.duration
                        let fileSize = info.fileSize
                        let detailsVC = VideoEditingDetailsViewController(
                            videoPath: videoPath,
                            coverImage: coverImage,
                            videoDuration: duration,
                            videoSize: fileSize
                        )
                        detailsVC.modalPresentationStyle = .fullScreen
                        self.present(detailsVC, animated: true)
                    } else {
                        // 兜底：只传封面

                    }
                } else {
                    let alert = UIAlertController(title: "合成失败", message: result.descMsg, preferredStyle: .alert)
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    self.present(alert, animated: true)
                }
            }
        }
    }

    // MARK: - 存草稿相关方法

    /// 获取腾讯云上传签名
    private func getTXSign() {
        // 显示加载状态
        draftButton.isEnabled = false
        draftButton.setTitle("获取签名...", for: .normal)

        // 调用 API 获取腾讯云上传签名
        APIManager.shared.getVodSignature { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.status == 200, let signData = response.data {
                        // 保存签名
                        self.uploadSignature = signData.sign
                        print("[存草稿] 成功获取上传签名: \(signData.sign)")
                        // 获取签名成功，开始上传视频到腾讯云
                        self.startVideoUpload()
                    } else {
                        // 失败处理
                        self.isSavingDraft = false
                        self.draftButton.isEnabled = true
                        self.draftButton.setTitle("存草稿", for: .normal)
                        let message = response.errMsg.isEmpty ? "获取上传签名失败" : response.errMsg
                        let alert = UIAlertController(title: "签名获取失败", message: message, preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default))
                        self.present(alert, animated: true)
                    }
                case .failure(let error):
                    self.isSavingDraft = false
                    self.draftButton.isEnabled = true
                    self.draftButton.setTitle("存草稿", for: .normal)
                    let alert = UIAlertController(title: "网络错误", message: error.localizedDescription, preferredStyle: .alert)
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    self.present(alert, animated: true)
                }
            }
        }
    }

    /// 开始调用 TXUGCPublish 上传视频
    private func startVideoUpload() {
        guard let signature = uploadSignature, let videoPath = generatedVideoPath else {
            print("[存草稿] 上传失败: signature=\(uploadSignature ?? "nil"), videoPath=\(generatedVideoPath ?? "nil")")
            isSavingDraft = false
            draftButton.isEnabled = true
            draftButton.setTitle("存草稿", for: .normal)
            let alert = UIAlertController(title: "错误", message: "缺少上传签名或视频路径", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }

        print("[存草稿] 开始上传视频: \(videoPath)")
        print("[存草稿] 使用签名: \(signature)")

        // 再次检查文件是否存在
        let fileExists = FileManager.default.fileExists(atPath: videoPath)
        print("[存草稿] 上传前文件检查: \(fileExists)")

        if !fileExists {
            isSavingDraft = false
            draftButton.isEnabled = true
            draftButton.setTitle("存草稿", for: .normal)
            let alert = UIAlertController(title: "错误", message: "视频文件不存在，无法上传", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }

        // 构造发布参数
        let param = TXPublishParam()
        param.signature = signature
        param.videoPath = videoPath
        param.enableResume = true
        param.enableHTTPS = true

        // 使用视频第一帧作为封面
        if let info = TXVideoInfoReader.getVideoInfo(videoPath) {
            let coverImage = info.coverImage
            let tempDir = URL(fileURLWithPath: NSTemporaryDirectory(), isDirectory: true)
            let coverURL = tempDir.appendingPathComponent("draft_cover_\(Int(Date().timeIntervalSince1970)).jpg")
            if let data = coverImage.jpegData(compressionQuality: 0.85) {
                do {
                    try data.write(to: coverURL)
                    param.coverPath = coverURL.path
                } catch {
                    print("[存草稿] 保存封面失败: \(error)")
                }
            }
        }

        // 创建发布器
        ugcPublisher = TXUGCPublish()
        ugcPublisher?.delegate = self

        // 显示上传进度
        showUploadProgress()

        // 开始上传
        let result = ugcPublisher?.publishVideo(param)
        if result != 0 {
            hideUploadProgress()
            isSavingDraft = false
            draftButton.isEnabled = true
            draftButton.setTitle("存草稿", for: .normal)
            let alert = UIAlertController(title: "上传失败", message: "启动上传失败", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }
    }

    /// 显示上传进度弹窗
    private func showUploadProgress() {
        publishProgressAlert = UIAlertController(title: "正在上传", message: "\n\n", preferredStyle: .alert)

        // 创建进度条
        publishProgressView = UIProgressView(progressViewStyle: .default)
        publishProgressView?.progress = 0.0
        publishProgressView?.translatesAutoresizingMaskIntoConstraints = false

        publishProgressAlert?.view.addSubview(publishProgressView!)
        publishProgressView?.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(80)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(4)
        }

        present(publishProgressAlert!, animated: true)
    }

    /// 隐藏上传进度弹窗
    private func hideUploadProgress() {
        publishProgressAlert?.dismiss(animated: true)
        publishProgressAlert = nil
        publishProgressView = nil
    }
}

// MARK: - TXVideoPublishListener
extension VideoEditViewController {

    func onPublishProgress(_ uploadBytes: Int32, totalBytes: Int32) {
        DispatchQueue.main.async {
            let progress = totalBytes == 0 ? 0 : Float(uploadBytes) / Float(totalBytes)
            self.publishProgressView?.progress = progress
            let percent = Int(progress * 100)
            self.publishProgressAlert?.message = "上传进度: \(percent)%"
        }
    }

    func onPublishComplete(_ result: TXPublishResult) {
        DispatchQueue.main.async {
            self.hideUploadProgress()

            print("[存草稿] 上传完成: retCode=\(result.retCode), descMsg=\(result.descMsg ?? ""), videoURL=\(result.videoURL ?? ""), coverURL=\(result.coverURL ?? "")")

            if result.retCode == 0 {
                // 上传成功，调用保存草稿接口
                print("[存草稿] 上传成功，开始保存草稿到服务器")
                self.saveDraftToServer(videoUrl: result.videoURL ?? "", coverUrl: result.coverURL ?? "")
            } else {
                // 上传失败
                print("[存草稿] 上传失败: \(result.descMsg ?? "")")
                self.isSavingDraft = false
                self.draftButton.isEnabled = true
                self.draftButton.setTitle("存草稿", for: .normal)

                let message = result.descMsg ?? "上传失败"
                let alert = UIAlertController(title: "上传失败", message: message, preferredStyle: .alert)
                alert.addAction(UIAlertAction(title: "确定", style: .default))
                self.present(alert, animated: true)
            }
        }
    }

    /// 保存草稿到服务器
    private func saveDraftToServer(videoUrl: String, coverUrl: String) {
        draftButton.setTitle("保存中...", for: .normal)

        // 构造保存草稿的参数
        var params: [String: Any] = [:]
        params["worksTitle"] = "" // 标题传空
        params["worksUrl"] = [videoUrl]
        params["worksCoverImg"] = coverUrl
        params["worksType"] = 1 // 1-视频
        params["duration"] = Int(videoDuration)

        // 获取视频文件大小
        if let videoPath = generatedVideoPath {
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: videoPath)
                if let fileSize = attributes[.size] as? UInt64 {
                    params["size"] = Int(fileSize)
                }
            } catch {
                print("[存草稿] 获取文件大小失败: \(error)")
            }
        }

        // 其他默认参数
        params["categoryId"] = 1 // 默认分类
        params["privacy"] = 1 // 1-公开
        params["allowComment"] = true
        params["followComment"] = false

        print("[存草稿] 保存草稿参数: \(params)")

        // 调用保存草稿接口
        APIManager.shared.saveVideoWorksDrafts(params: params) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }

                self.isSavingDraft = false
                self.draftButton.isEnabled = true
                self.draftButton.setTitle("存草稿", for: .normal)

                switch result {
                case .success(_):
                    // 保存成功
                    if self.shouldExitAfterSavingDraft {
                        // 从返回按钮触发的保存草稿，直接返回
                        let alert = UIAlertController(title: "保存成功", message: "草稿已保存！", preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                            if let navigationController = self.navigationController {
                                navigationController.popViewController(animated: true)
                            } else {
                                self.dismiss(animated: true)
                            }
                        })
                        self.present(alert, animated: true)
                        self.shouldExitAfterSavingDraft = false // 重置标志
                    } else {
                        // 从存草稿按钮触发的保存草稿，跳转到草稿箱
                        let alert = UIAlertController(title: "保存成功", message: "草稿已保存！", preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                            self.navigateToDraftBox()
                        })
                        self.present(alert, animated: true)
                    }

                case .failure(let error):
                    self.shouldExitAfterSavingDraft = false // 重置标志
                    let alert = UIAlertController(title: "保存失败", message: error.errorMessage, preferredStyle: .alert)
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    self.present(alert, animated: true)
                }
            }
        }
    }

    /// 跳转到草稿箱
    private func navigateToDraftBox() {
        let draftBoxVC = VideoDraftBoxViewController()
        draftBoxVC.fromEditPage = true // 标记来源于编辑页

        if let navigationController = self.navigationController {
            navigationController.pushViewController(draftBoxVC, animated: true)
        } else {
            let navController = UINavigationController(rootViewController: draftBoxVC)
            navController.modalPresentationStyle = .fullScreen
            self.present(navController, animated: true)
        }
    }
}

// MARK: - 垂直图标按钮（图标在上，文字在下，间距1pt）
class VerticalIconButton: UIView {
    let imageView = UIImageView()
    let titleLabel = UILabel()
    
    init(image: UIImage?, title: String) {
        super.init(frame: .zero)
        imageView.image = image
        imageView.contentMode = .scaleAspectFit
        imageView.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.text = title
        // 增强文字内阴影效果
        titleLabel.layer.shadowColor = UIColor.black.withAlphaComponent(0.7).cgColor
        titleLabel.layer.shadowOffset = CGSize(width: 0, height: 1)
        titleLabel.layer.shadowOpacity = 1
        titleLabel.layer.shadowRadius = 2
        titleLabel.layer.masksToBounds = false
        titleLabel.font = .systemFont(ofSize: 15)
        titleLabel.textColor = .white
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        let stack = UIStackView(arrangedSubviews: [imageView, titleLabel])
        stack.axis = .vertical
        stack.alignment = .center
        stack.spacing = 1
        stack.translatesAutoresizingMaskIntoConstraints = false
        addSubview(stack)
        NSLayoutConstraint.activate([
            stack.topAnchor.constraint(equalTo: topAnchor),
            stack.bottomAnchor.constraint(equalTo: bottomAnchor),
            stack.leadingAnchor.constraint(equalTo: leadingAnchor),
            stack.trailingAnchor.constraint(equalTo: trailingAnchor),
            imageView.widthAnchor.constraint(equalToConstant: 50),
            imageView.heightAnchor.constraint(equalToConstant: 50)
        ])
        // 让文字向上偏移10pt
        stack.setCustomSpacing(-9, after: imageView)
        isUserInteractionEnabled = true
        // 整体外阴影
        layer.shadowColor = UIColor.black.withAlphaComponent(0.4).cgColor
        layer.shadowOffset = CGSize(width: 0, height: 4)
        layer.shadowOpacity = 1
        layer.shadowRadius = 8
        layer.masksToBounds = false
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
}

// MARK: - 弹窗面板
class SimplePanelView: UIView {
    enum PanelType { case music, filter, beauty }
    var onSelect: ((String) -> Void)?
    private let type: PanelType
    private let options: [String]
    init(type: PanelType, options: [String]) {
        self.type = type
        self.options = options
        super.init(frame: .zero)
        backgroundColor = UIColor.black.withAlphaComponent(0.95)
        layer.cornerRadius = 16
        let stack = UIStackView()
        stack.axis = .vertical
        stack.alignment = .center
        stack.spacing = 16
        stack.translatesAutoresizingMaskIntoConstraints = false
        for opt in options {
            let btn = UIButton(type: .system)
            btn.setTitle(opt, for: .normal)
            btn.setTitleColor(.white, for: .normal)
            btn.titleLabel?.font = .systemFont(ofSize: 18, weight: .medium)
            btn.addTarget(self, action: #selector(optionTapped(_:)), for: .touchUpInside)
            stack.addArrangedSubview(btn)
        }
        addSubview(stack)
        NSLayoutConstraint.activate([
            stack.topAnchor.constraint(equalTo: topAnchor, constant: 24),
            stack.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -24),
            stack.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 24),
            stack.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -24)
        ])
    }
    @objc private func optionTapped(_ sender: UIButton) {
        guard let title = sender.title(for: .normal) else { return }
        onSelect?(title)
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
}
